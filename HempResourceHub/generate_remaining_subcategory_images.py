#!/usr/bin/env python3
"""
Generate images for all remaining hemp industry subcategories
"""

import requests
import os
import time
import json
import sys

# Get subcategories that don't have custom images yet
REMAINING_SUBCATEGORIES = [
    # Textiles & Fashion (continuing from where we left off)
    {"id": 5, "name": "Yarns & Fabrics", "industry": "textiles"},
    {"id": 2, "name": "Footwear", "industry": "textiles"},
    {"id": 30, "name": "Hemp Apparel", "industry": "textiles"},
    {"id": 32, "name": "Hemp Fiber Processing", "industry": "textiles"},
    {"id": 3, "name": "Home Textiles", "industry": "textiles"},
    {"id": 31, "name": "Technical Textiles", "industry": "textiles"},

    # Food & Beverages - High Priority
    {"id": 26, "name": "Hemp Seeds", "industry": "food"},
    {"id": 27, "name": "Hemp Seed Oil", "industry": "food"},
    {"id": 28, "name": "Hemp Protein", "industry": "food"},
    {"id": 10, "name": "Baked Goods", "industry": "food"},
    {"id": 11, "name": "Hemp Beverages", "industry": "food"},
    {"id": 12, "name": "Hemp Protein Foods", "industry": "food"},
    {"id": 13, "name": "Hemp Seed Oil Products", "industry": "food"},
    {"id": 37, "name": "Protein Ingredients", "industry": "food"},

    # Construction (remaining)
    {"id": 6, "name": "Composites", "industry": "construction"},

    # Energy & Biofuels
    {"id": 22, "name": "Hemp Batteries/Supercapacitors", "industry": "energy"},
    {"id": 46, "name": "Biofuels", "industry": "energy"},
    {"id": 44, "name": "Advanced Energy Storage", "industry": "energy"},
    {"id": 41, "name": "Aviation Fuels", "industry": "energy"},
    {"id": 35, "name": "Energy Storage", "industry": "energy"},

    # Personal Care & Beauty
    {"id": 17, "name": "Skincare", "industry": "beauty"},
    {"id": 15, "name": "Body Care", "industry": "beauty"},
    {"id": 16, "name": "Hair Care", "industry": "beauty"},

    # Manufacturing & Materials
    {"id": 24, "name": "Hemp Plastic Products", "industry": "manufacturing"},
    {"id": 38, "name": "3D Printing Materials", "industry": "manufacturing"},

    # Paper & Packaging
    {"id": 36, "name": "Sustainable Packaging", "industry": "paper"},
    {"id": 45, "name": "Paper Products", "industry": "paper"},

    # Animal Care & Nutrition
    {"id": 52, "name": "Pet Food", "industry": "animal"},
    {"id": 53, "name": "Animal Bedding", "industry": "animal"},
    {"id": 54, "name": "Feed Supplements", "industry": "animal"},
    {"id": 55, "name": "Veterinary Products", "industry": "animal"},

    # Medical & Therapeutic
    {"id": 20, "name": "CBD Products", "industry": "medical"},
    {"id": 18, "name": "Therapeutic Applications", "industry": "medical"},
    {"id": 19, "name": "Minor Cannabinoid Products", "industry": "medical"},

    # Composites & Advanced Materials
    {"id": 60, "name": "Biocomposites", "industry": "materials"},
    {"id": 61, "name": "Nanofibers", "industry": "materials"},
    {"id": 62, "name": "Carbon Fiber Alternatives", "industry": "materials"},
    {"id": 63, "name": "Advanced Polymers", "industry": "materials"},

    # Electronics & Technology
    {"id": 42, "name": "Electronics Components", "industry": "electronics"},
    {"id": 64, "name": "Circuit Boards", "industry": "electronics"},
    {"id": 65, "name": "Semiconductors", "industry": "electronics"},
    {"id": 66, "name": "Display Materials", "industry": "electronics"},
    {"id": 67, "name": "Electronic Housings", "industry": "electronics"},
]

# Enhanced prompts for different industries
PROMPTS = {
    "textiles": "Professional textile photography of {name}, hemp fabric material, clean studio lighting, textile industry aesthetic, high quality fabric texture",
    "food": "Premium food photography of {name}, hemp food ingredient, natural kitchen setting, appetizing presentation, food styling lighting",
    "construction": "Professional construction photography of {name}, hemp building material, modern construction site, architectural photography lighting",
    "energy": "Clean technology photography of {name}, sustainable hemp energy solution, modern laboratory setting, high-tech aesthetic",
    "beauty": "Luxury cosmetics photography of {name}, hemp beauty product, spa environment, soft professional lighting, premium beauty aesthetic",
    "manufacturing": "Industrial photography of {name}, hemp manufacturing material, modern factory setting, professional industrial lighting",
    "paper": "Product photography of {name}, sustainable hemp paper product, clean office environment, eco-friendly aesthetic",
    "animal": "Veterinary photography of {name}, hemp animal care product, clean pet care setting, natural lighting, pet-friendly environment",
    "medical": "Medical photography of {name}, hemp therapeutic product, clinical setting, professional medical lighting, health-focused aesthetic",
    "materials": "Scientific photography of {name}, advanced hemp material, research laboratory, technical lighting, materials science aesthetic",
    "electronics": "Technology photography of {name}, hemp electronics component, clean tech lab, professional tech lighting, modern electronics aesthetic",
}

def generate_image_batch(subcategory):
    """Generate an image for a subcategory with optimized settings"""

    prompt_template = PROMPTS.get(subcategory["industry"], PROMPTS["textiles"])
    prompt = prompt_template.format(name=subcategory["name"])

    print(f"Generating: {subcategory['name']} (ID: {subcategory['id']})")

    url = "https://api.replicate.com/v1/predictions"
    headers = {
        "Authorization": "Bearer ****************************************",
        "Content-Type": "application/json"
    }

    payload = {
        "version": "ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4",
        "input": {
            "prompt": prompt,
            "width": 512,
            "height": 384,  # 4:3 aspect ratio for cards
            "num_outputs": 1,
            "num_inference_steps": 15,
            "guidance_scale": 7.0,
            "scheduler": "K_EULER"
        }
    }

    response = requests.post(url, headers=headers, json=payload)
    if response.status_code != 201:
        print(f"Error: {response.status_code}")
        return None

    prediction = response.json()
    prediction_id = prediction["id"]
    get_url = f"https://api.replicate.com/v1/predictions/{prediction_id}"

    # Poll for completion
    for attempt in range(30):  # 2.5 minutes max
        time.sleep(5)

        response = requests.get(get_url, headers=headers)
        if response.status_code != 200:
            continue

        prediction = response.json()
        status = prediction["status"]

        if status == "succeeded" and prediction.get("output"):
            return download_image(prediction["output"][0], subcategory)
        elif status == "failed":
            print(f"Failed: {prediction.get('error', 'Unknown error')}")
            return None

    print("Timeout")
    return None

def download_image(image_url, subcategory):
    """Download and save the image"""
    try:
        os.makedirs("client/public/images/subcategories", exist_ok=True)

        safe_name = subcategory["name"].lower().replace(" ", "-").replace("/", "-").replace("&", "and")
        filename = f"{subcategory['id']:03d}-{safe_name}.jpg"
        filepath = f"client/public/images/subcategories/{filename}"

        response = requests.get(image_url)
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                f.write(response.content)

            public_url = f"/images/subcategories/{filename}"
            print(f"✓ Saved: {filename}")
            return public_url

    except Exception as e:
        print(f"Download error: {e}")

    return None

def main():
    """Generate images in batches"""
    batch_size = int(sys.argv[1]) if len(sys.argv) > 1 else 10
    start_index = int(sys.argv[2]) if len(sys.argv) > 2 else 0

    end_index = min(start_index + batch_size, len(REMAINING_SUBCATEGORIES))
    batch = REMAINING_SUBCATEGORIES[start_index:end_index]

    print(f"Generating batch {start_index//batch_size + 1}: items {start_index+1}-{end_index} of {len(REMAINING_SUBCATEGORIES)}")

    results = []
    successful_updates = []

    for i, subcategory in enumerate(batch):
        print(f"\n[{i+1}/{len(batch)}] Processing: {subcategory['name']}")

        image_url = generate_image_batch(subcategory)

        if image_url:
            results.append({
                "id": subcategory["id"],
                "name": subcategory["name"],
                "image_url": image_url
            })
            successful_updates.append(f"UPDATE industry_sub_categories SET image_url = '{image_url}' WHERE id = {subcategory['id']};")

        # Brief pause between requests
        time.sleep(2)

    print(f"\n=== BATCH COMPLETE ===")
    print(f"Generated {len(results)} images successfully")

    if successful_updates:
        # Append to existing SQL file
        with open("subcategory_updates.sql", "a") as f:
            f.write("\n" + "\n".join(successful_updates))
        print(f"SQL updates appended to: subcategory_updates.sql")

        # Also save batch results
        batch_file = f"batch_results_{start_index//batch_size + 1}.json"
        with open(batch_file, "w") as f:
            json.dump(results, f, indent=2)
        print(f"Batch results saved to: {batch_file}")

    remaining = len(REMAINING_SUBCATEGORIES) - end_index
    if remaining > 0:
        print(f"\n{remaining} subcategories remaining. Run next batch with:")
        print(f"python3 generate_remaining_subcategory_images.py {batch_size} {end_index}")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Generate images for the final 17 subcategories still using fallback images
"""

import requests
import os
import time
import json
import sys

# Final missing subcategories with fallback images
FINAL_MISSING = [
    {"id": 14, "name": "Hemp Seeds", "industry": "food"},
    {"id": 20, "name": "CBD Products", "industry": "medical"},
    {"id": 21, "name": "Vehicle Components", "industry": "automotive"},
    {"id": 25, "name": "Phytoremediation", "industry": "environmental"},
    {"id": 39, "name": "Insulation Materials", "industry": "construction"},
    {"id": 43, "name": "Prefab Building Systems", "industry": "construction"},
    {"id": 47, "name": "Environmental Services", "industry": "environmental"},
    {"id": 48, "name": "Composite Panels", "industry": "aerospace"},
    {"id": 50, "name": "Structural Materials", "industry": "aerospace"},
    {"id": 51, "name": "Insulation Systems", "industry": "aerospace"},
    {"id": 58, "name": "Industrial Cleaners", "industry": "cleaning"},
    {"id": 72, "name": "Material Science", "industry": "research"},
    {"id": 75, "name": "Medical Research", "industry": "research"},
    {"id": 81, "name": "Functional Foods", "industry": "food"},
    {"id": 84, "name": "Soil Amendment", "industry": "agriculture"},
    {"id": 85, "name": "Carbon Sequestration", "industry": "environmental"},
    {"id": 87, "name": "Organic Farming", "industry": "agriculture"},
]

# Enhanced prompts for specific industries
PROMPTS = {
    "food": "Premium food photography of {name}, hemp food ingredient, natural kitchen setting, appetizing presentation, food styling lighting",
    "medical": "Professional medical photography of {name}, hemp wellness product, clean clinical setting, professional medical lighting",
    "automotive": "Automotive photography of {name}, hemp vehicle component, modern car manufacturing, professional automotive lighting",
    "environmental": "Environmental photography of {name}, hemp environmental solution, natural outdoor setting, environmental documentation lighting",
    "construction": "Professional construction photography of {name}, hemp building material, modern construction site, architectural photography",
    "aerospace": "Aerospace photography of {name}, hemp aerospace material, aircraft facility, professional aerospace photography",
    "cleaning": "Product photography of {name}, eco-friendly hemp cleaning product, modern home setting, clean natural lighting",
    "research": "Scientific photography of {name}, hemp research application, modern laboratory setting, professional scientific lighting",
    "agriculture": "Agricultural photography of {name}, hemp farming solution, farm field setting, natural agricultural lighting",
}

def generate_final_image(subcategory):
    """Generate an image for final missing subcategory"""

    prompt_template = PROMPTS.get(subcategory["industry"], PROMPTS["construction"])

    # Special handling for sensitive terms
    if "CBD" in subcategory["name"]:
        prompt = f"Professional product photography of hemp wellness oil bottles, clean pharmacy setting, medical grade lighting, health product aesthetic"
    else:
        prompt = prompt_template.format(name=subcategory["name"])

    print(f"Generating: {subcategory['name']} (ID: {subcategory['id']})")

    url = "https://api.replicate.com/v1/predictions"
    headers = {
        "Authorization": "Bearer ****************************************",
        "Content-Type": "application/json"
    }

    payload = {
        "version": "ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4",
        "input": {
            "prompt": prompt,
            "width": 512,
            "height": 384,
            "num_outputs": 1,
            "num_inference_steps": 15,
            "guidance_scale": 7.0,
            "scheduler": "K_EULER"
        }
    }

    response = requests.post(url, headers=headers, json=payload)
    if response.status_code != 201:
        print(f"Error: {response.status_code}")
        return None

    prediction = response.json()
    prediction_id = prediction["id"]
    get_url = f"https://api.replicate.com/v1/predictions/{prediction_id}"

    # Poll for completion
    for attempt in range(30):
        time.sleep(5)

        response = requests.get(get_url, headers=headers)
        if response.status_code != 200:
            continue

        prediction = response.json()
        status = prediction["status"]

        if status == "succeeded" and prediction.get("output"):
            return download_image(prediction["output"][0], subcategory)
        elif status == "failed":
            print(f"Failed: {prediction.get('error', 'Unknown error')}")
            return None

    print("Timeout")
    return None

def download_image(image_url, subcategory):
    """Download and save the image"""
    try:
        os.makedirs("client/public/images/subcategories", exist_ok=True)

        safe_name = subcategory["name"].lower().replace(" ", "-").replace("/", "-").replace("&", "and")
        filename = f"{subcategory['id']:03d}-{safe_name}.jpg"
        filepath = f"client/public/images/subcategories/{filename}"

        response = requests.get(image_url)
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                f.write(response.content)

            public_url = f"/images/subcategories/{filename}"
            print(f"✓ Saved: {filename}")
            return public_url

    except Exception as e:
        print(f"Download error: {e}")

    return None

def main():
    """Generate images for final missing subcategories"""
    batch_size = int(sys.argv[1]) if len(sys.argv) > 1 else 8
    start_index = int(sys.argv[2]) if len(sys.argv) > 2 else 0

    end_index = min(start_index + batch_size, len(FINAL_MISSING))
    batch = FINAL_MISSING[start_index:end_index]

    print(f"Generating final missing images: items {start_index+1}-{end_index} of {len(FINAL_MISSING)}")

    results = []
    successful_updates = []

    for i, subcategory in enumerate(batch):
        print(f"\n[{i+1}/{len(batch)}] Processing: {subcategory['name']}")

        image_url = generate_final_image(subcategory)

        if image_url:
            results.append({
                "id": subcategory["id"],
                "name": subcategory["name"],
                "image_url": image_url
            })
            successful_updates.append(f"UPDATE industry_sub_categories SET image_url = '{image_url}' WHERE id = {subcategory['id']};")

        time.sleep(2)

    print(f"\n=== BATCH COMPLETE ===")
    print(f"Generated {len(results)} images successfully")

    if successful_updates:
        # Save to new SQL file
        with open("final_subcategory_updates.sql", "a") as f:
            f.write("\n".join(successful_updates) + "\n")
        print(f"SQL updates saved to: final_subcategory_updates.sql")

        # Save batch results
        batch_file = f"final_batch_results_{start_index//batch_size + 1}.json"
        with open(batch_file, "w") as f:
            json.dump(results, f, indent=2)
        print(f"Batch results saved to: {batch_file}")

    remaining = len(FINAL_MISSING) - end_index
    if remaining > 0:
        print(f"\n{remaining} subcategories remaining. Run next batch with:")
        print(f"python3 generate_final_missing_images.py {batch_size} {end_index}")

if __name__ == "__main__":
    main()
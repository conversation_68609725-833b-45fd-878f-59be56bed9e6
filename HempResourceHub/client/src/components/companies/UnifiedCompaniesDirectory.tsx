import React, { useState, useMemo } from "react";
import { <PERSON> } from "wouter";
import {
  Building2,
  MapPin,
  Globe,
  Users,
  Star,
  ExternalLink,
  Phone,
  Mail,
  Verified,
  TrendingUp,
  Award,
  Leaf,
  Factory,
  Package,
  Grid3X3,
  List
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { UnifiedExplorerLayout } from "@/components/layout/UnifiedExplorerLayout";
import { cn } from "@/lib/utils";
import { useCompanies, type HempCompany } from "@/hooks/use-companies";

// Transform Supabase company data to match component interface
interface Company {
  id: string;
  name: string;
  description: string;
  logo?: string;
  website?: string;
  location: string;
  country: string;
  industry: string;
  specialization: string[];
  employeeCount: string;
  foundedYear: number;
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  isPremium: boolean;
  contactEmail?: string;
  contactPhone?: string;
}

const transformSupabaseCompany = (company: HempCompany): Company => ({
  id: company.id.toString(),
  name: company.name,
  description: company.description || '',
  logo: company.logo_url || undefined,
  website: company.website || undefined,
  location: `${company.city || ''}, ${company.state_province || ''}`.replace(/, $/, ''),
  country: company.country || '',
  industry: company.company_type || 'Hemp Industry',
  specialization: [], // TODO: Extract from description or add specializations field
  employeeCount: 'Not specified', // TODO: Add employee count field
  foundedYear: company.founded_year || new Date().getFullYear(),
  rating: 4.0, // TODO: Add rating system
  reviewCount: 0, // TODO: Add review system
  isVerified: company.verified || false,
  isPremium: false, // TODO: Add premium feature
  contactEmail: undefined, // TODO: Add contact fields
  contactPhone: undefined, // TODO: Add contact fields
});

// Company Card Component
const CompanyCard: React.FC<{ company: Company; viewMode: string }> = ({ company, viewMode }) => {
  if (viewMode === 'list') {
    return (
      <Link href={`/hemp-companies/${company.id}`} className="block">
        <Card className="bg-gray-800/50 border-gray-700 hover:border-green-500/50 transition-colors duration-300 hover:shadow-lg hover:shadow-green-500/10 cursor-pointer">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              {/* Company Logo - Centered */}
              <div className="flex justify-center">
                <div className="w-32 h-32 bg-gray-800 rounded-xl flex items-center justify-center">
                  {company.logo ? (
                    <img src={company.logo} alt={company.name} className="w-28 h-28 rounded-lg object-cover" />
                  ) : (
                    <Building2 className="h-16 w-16 text-green-400" />
                  )}
                </div>
              </div>

              {/* Company Name - Centered */}
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <h3 className="text-2xl font-bold text-white hover:text-green-400 transition-colors">
                    {company.name}
                  </h3>
                  {company.isVerified && (
                    <Verified className="h-5 w-5 text-blue-400" />
                  )}
                  {company.isPremium && (
                    <Award className="h-5 w-5 text-yellow-400" />
                  )}
                </div>
                {company.location && company.location !== 'Not specified' && (
                  <div className="flex items-center justify-center gap-1 text-sm text-gray-400">
                    <MapPin className="h-3 w-3" />
                    {company.location}
                  </div>
                )}
              </div>

              {/* Description - Centered */}
              {company.description && company.description !== 'Not specified' && (
                <p className="text-gray-300 text-sm line-clamp-3 text-center">{company.description}</p>
              )}

              {/* Industry Badge - Centered */}
              {company.industry && company.industry !== 'Not specified' && (
                <div className="flex justify-center">
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
                    {company.industry}
                  </Badge>
                </div>
              )}

              {/* Rating - Centered */}
              {company.rating && (
                <div className="flex items-center justify-center gap-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-white">{company.rating}</span>
                  {company.reviewCount > 0 && (
                    <span className="text-sm text-gray-400">({company.reviewCount})</span>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </Link>
    );
  }

  // Grid view
  return (
    <Link href={`/hemp-companies/${company.id}`} className="block">
      <Card className="bg-gray-800/50 border-gray-700 hover:border-green-500/50 transition-colors duration-300 hover:shadow-lg hover:shadow-green-500/10 group cursor-pointer">
        <CardContent className="p-6">
          <div className="space-y-4 text-center">
            {/* Company Logo - Prominent and Centered */}
            <div className="flex justify-center">
              <div className="w-24 h-24 bg-gray-800 rounded-xl flex items-center justify-center">
                {company.logo ? (
                  <img src={company.logo} alt={company.name} className="w-20 h-20 rounded-lg object-cover" />
                ) : (
                  <Building2 className="h-12 w-12 text-green-400" />
                )}
              </div>
            </div>

            {/* Company Name - Large and Centered */}
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-2">
                <h3 className="text-xl font-bold text-white group-hover:text-green-400 transition-colors line-clamp-2">
                  {company.name}
                </h3>
                {company.isVerified && <Verified className="h-4 w-4 text-blue-400" />}
                {company.isPremium && <Award className="h-4 w-4 text-yellow-400" />}
              </div>

              {/* Location and Rating - Centered */}
              <div className="flex items-center justify-center gap-4 text-sm text-gray-400">
                {company.location && company.location !== 'Not specified' && (
                  <span className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {company.location}
                  </span>
                )}
                {company.rating && (
                  <span className="flex items-center gap-1">
                    <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    {company.rating}
                  </span>
                )}
              </div>
            </div>

            {/* Description - Centered */}
            {company.description && company.description !== 'Not specified' && (
              <p className="text-sm text-gray-300 line-clamp-3 text-center">{company.description}</p>
            )}

            {/* Industry Badge - Centered */}
            {company.industry && company.industry !== 'Not specified' && (
              <div className="flex justify-center">
                <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                  {company.industry}
                </Badge>
              </div>
            )}

            {/* Employee Count and Founded Year - Centered */}
            <div className="pt-2 border-t border-gray-700">
              <div className="flex items-center justify-center gap-4 text-xs text-gray-400">
                {company.employeeCount && company.employeeCount !== 'Not specified' && (
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {company.employeeCount}
                  </span>
                )}
                {company.foundedYear && company.foundedYear !== 'Not specified' && (
                  <span>{company.foundedYear}</span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export const UnifiedCompaniesDirectory: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedIndustry, setSelectedIndustry] = useState("All Industries");
  const [selectedLocation, setSelectedLocation] = useState("All Locations");
  const [sortBy, setSortBy] = useState("name");
  const [viewMode, setViewMode] = useState("grid");

  const { data: companiesData = [], isLoading } = useCompanies();

  // Transform data
  const companies = useMemo(() =>
    companiesData.map(transformSupabaseCompany),
    [companiesData]
  );

  // Extract filter options
  const industries = useMemo(() => {
    const uniqueIndustries = new Set(companies.map(c => c.industry));
    return ["All Industries", ...Array.from(uniqueIndustries)];
  }, [companies]);

  const locations = useMemo(() => {
    const uniqueLocations = new Set(companies.map(c => c.country).filter(Boolean));
    return ["All Locations", ...Array.from(uniqueLocations)];
  }, [companies]);

  // Filter and sort companies
  const filteredCompanies = useMemo(() => {
    return companies
      .filter(company => {
        const matchesSearch = searchQuery === "" ||
          company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          company.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          company.specialization.some(spec => spec.toLowerCase().includes(searchQuery.toLowerCase()));

        const matchesIndustry = selectedIndustry === "All Industries" || company.industry === selectedIndustry;
        const matchesLocation = selectedLocation === "All Locations" || company.country === selectedLocation;

        return matchesSearch && matchesIndustry && matchesLocation;
      })
      .sort((a, b) => {
        switch (sortBy) {
          case "name": return a.name.localeCompare(b.name);
          case "rating": return b.rating - a.rating;
          case "founded": return b.foundedYear - a.foundedYear;
          default: return 0;
        }
      });
  }, [companies, searchQuery, selectedIndustry, selectedLocation, sortBy]);

  const clearAllFilters = () => {
    setSearchQuery("");
    setSelectedIndustry("All Industries");
    setSelectedLocation("All Locations");
    setSortBy("name");
  };

  // Layout configuration
  const statsCards = [
    {
      title: "Total Companies",
      value: companies.length,
      icon: Building2,
      iconColor: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/20"
    },
    {
      title: "Industries",
      value: industries.length - 1, // Exclude "All Industries"
      icon: Factory,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/20"
    },
    {
      title: "Countries",
      value: locations.length - 1, // Exclude "All Locations"
      icon: Globe,
      iconColor: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/20"
    },
    {
      title: "Filtered Results",
      value: filteredCompanies.length,
      icon: Package,
      iconColor: "text-orange-600",
      bgColor: "bg-orange-100 dark:bg-orange-900/20"
    }
  ];

  const filterConfig = {
    searchPlaceholder: "Search companies, specializations, or locations...",
    filters: [
      {
        key: "industry",
        label: "Industry",
        options: industries.map(industry => ({ value: industry, label: industry })),
        value: selectedIndustry,
        onChange: setSelectedIndustry
      },
      {
        key: "location",
        label: "Location",
        options: locations.map(location => ({ value: location, label: location })),
        value: selectedLocation,
        onChange: setSelectedLocation
      },
      {
        key: "sortBy",
        label: "Sort By",
        options: [
          { value: "name", label: "Company Name" },
          { value: "rating", label: "Rating" },
          { value: "founded", label: "Founded Year" }
        ],
        value: sortBy,
        onChange: setSortBy
      }
    ]
  };

  const viewModeConfig = {
    modes: [
      { key: "grid", label: "Grid", icon: Grid3X3 },
      { key: "list", label: "List", icon: List }
    ],
    current: viewMode,
    onChange: setViewMode
  };

  return (
    <UnifiedExplorerLayout
      title="Hemp Industry Directory"
      subtitle="Discover leading companies in the hemp industry. Connect with manufacturers, suppliers, and innovators driving the hemp economy forward."
      statsCards={statsCards}
      filterConfig={filterConfig}
      viewModeConfig={viewModeConfig}
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      onClearAllFilters={clearAllFilters}
      isLoading={isLoading}
      loadingSkeletonCount={12}
    >
      <div className={cn(
        "grid gap-6",
        viewMode === "grid"
          ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
          : "grid-cols-1"
      )}>
        {filteredCompanies.map((company) => (
          <CompanyCard
            key={company.id}
            company={company}
            viewMode={viewMode}
          />
        ))}
      </div>

      {filteredCompanies.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No companies found</h3>
          <p className="text-gray-400 mb-4">Try adjusting your search criteria or filters</p>
          <Button onClick={clearAllFilters} variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
            Clear all filters
          </Button>
        </div>
      )}
    </UnifiedExplorerLayout>
  );
};
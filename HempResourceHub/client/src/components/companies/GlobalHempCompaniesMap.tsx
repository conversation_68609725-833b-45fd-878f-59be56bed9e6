import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { useCompaniesWithLocation, HempCompany } from '@/hooks/use-companies';
import {
  MapPin,
  Building2,
  Globe,
  Users,
  TrendingUp,
  ExternalLink,
  Verified,
  Award,
  Filter,
  Search,
  Layers,
  Maximize2
} from 'lucide-react';
import { Link } from 'wouter';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in React Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom hemp company marker icon
const createHempMarker = (count: number, isSelected: boolean = false) => {
  const size = Math.max(30, Math.min(50, count * 5 + 25));
  const color = isSelected ? '#22c55e' : count >= 10 ? '#ef4444' : count >= 5 ? '#f59e0b' : count >= 2 ? '#3b82f6' : '#6b7280';

  return L.divIcon({
    className: 'custom-hemp-marker',
    html: `
      <div style="
        width: ${size}px;
        height: ${size}px;
        background: ${color};
        border: 3px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        font-weight: bold;
        color: white;
        font-size: ${Math.max(10, size / 4)}px;
        position: relative;
        transform: translate(-50%, -50%);
      ">
        ${count}
        <div style="
          position: absolute;
          top: -2px;
          right: -2px;
          width: 12px;
          height: 12px;
          background: #22c55e;
          border: 2px solid white;
          border-radius: 50%;
          animation: pulse 2s infinite;
        "></div>
      </div>
      <style>
        @keyframes pulse {
          0% { transform: scale(1); opacity: 1; }
          50% { transform: scale(1.2); opacity: 0.7; }
          100% { transform: scale(1); opacity: 1; }
        }
      </style>
    `,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
    popupAnchor: [0, -size / 2]
  });
};

interface CountryStats {
  country: string;
  count: number;
  companies: HempCompany[];
  center: [number, number];
}

interface MapControlsProps {
  onLayerChange: (layer: string) => void;
  currentLayer: string;
}

const MapControls: React.FC<MapControlsProps> = ({ onLayerChange, currentLayer }) => {
  const layers = [
    { key: 'satellite', label: 'Satellite', icon: '🛰️' },
    { key: 'terrain', label: 'Terrain', icon: '🏔️' },
    { key: 'street', label: 'Street', icon: '🗺️' }
  ];

  return (
    <div className="absolute top-2 right-2 sm:top-4 sm:right-4 z-[1000] bg-gray-800/90 backdrop-blur-sm rounded-lg border border-gray-600 p-1 sm:p-2">
      {/* Mobile: Horizontal layout */}
      <div className="flex sm:hidden gap-1">
        {layers.map((layer) => (
          <Button
            key={layer.key}
            variant={currentLayer === layer.key ? "default" : "ghost"}
            size="sm"
            className={`text-xs px-2 py-1 ${
              currentLayer === layer.key
                ? 'bg-green-500 text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            }`}
            onClick={() => onLayerChange(layer.key)}
          >
            <span>{layer.icon}</span>
          </Button>
        ))}
      </div>

      {/* Desktop: Vertical layout */}
      <div className="hidden sm:flex flex-col gap-1">
        {layers.map((layer) => (
          <Button
            key={layer.key}
            variant={currentLayer === layer.key ? "default" : "ghost"}
            size="sm"
            className={`text-xs px-2 py-1 ${
              currentLayer === layer.key
                ? 'bg-green-500 text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'
            }`}
            onClick={() => onLayerChange(layer.key)}
          >
            <span className="mr-1">{layer.icon}</span>
            {layer.label}
          </Button>
        ))}
      </div>
    </div>
  );
};

const CompanyPopup: React.FC<{ companies: HempCompany[] }> = ({ companies }) => {
  const displayCompanies = companies.slice(0, 3);
  const hasMore = companies.length > 3;

  return (
    <div className="w-64 max-h-80 overflow-y-auto">
      <div className="mb-2">
        <h3 className="font-semibold text-gray-900 flex items-center gap-1">
          <Building2 className="h-4 w-4 text-green-600" />
          {companies.length === 1 ? companies[0].name : `${companies.length} Companies`}
        </h3>
        {companies.length > 1 && (
          <p className="text-xs text-gray-600">
            {companies[0].city}, {companies[0].country}
          </p>
        )}
      </div>

      <div className="space-y-2">
        {displayCompanies.map((company) => (
          <div key={company.id} className="border-b border-gray-200 pb-2 last:border-b-0">
            <div className="flex items-start gap-2">
              <div className="w-8 h-8 bg-gray-100 rounded-md flex items-center justify-center flex-shrink-0">
                {company.logo_url ? (
                  <img
                    src={company.logo_url}
                    alt={company.name}
                    className="w-6 h-6 rounded object-cover"
                  />
                ) : (
                  <Building2 className="h-4 w-4 text-gray-500" />
                )}
              </div>

              <div className="flex-grow min-w-0">
                <div className="flex items-center gap-1 mb-1">
                  <Link href={`/hemp-companies/${company.id}`}>
                    <span className="font-medium text-sm text-gray-900 hover:text-green-600 cursor-pointer">
                      {company.name}
                    </span>
                  </Link>
                  {company.verified && (
                    <Verified className="h-3 w-3 text-blue-500" />
                  )}
                </div>

                {company.company_type && (
                  <span className="inline-block px-2 py-0.5 bg-green-100 text-green-700 text-xs rounded-full">
                    {company.company_type}
                  </span>
                )}

                {company.description && (
                  <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                    {company.description.slice(0, 100)}...
                  </p>
                )}

                <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                  {company.founded_year && (
                    <span>Est. {company.founded_year}</span>
                  )}
                  {company.website && (
                    <a
                      href={company.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-green-600 hover:text-green-700"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}

        {hasMore && (
          <div className="text-center py-1">
            <span className="text-xs text-gray-500">
              +{companies.length - 3} more companies
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

// Main Component
export const GlobalHempCompaniesMap: React.FC = () => {
  const { data: companies = [], isLoading } = useCompaniesWithLocation();
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [mapLayer, setMapLayer] = useState('satellite');

  // Filter companies based on search
  const filteredCompanies = useMemo(() => {
    if (!searchQuery.trim()) return companies;

    return companies.filter(company =>
      company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.country?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.city?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.company_type?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [companies, searchQuery]);

  // Group companies by location
  const locationGroups = useMemo(() => {
    const groups = new Map<string, HempCompany[]>();

    filteredCompanies.forEach(company => {
      if (company.latitude && company.longitude) {
        // Group companies that are very close together (within ~1km)
        const key = `${Math.round(company.latitude * 100) / 100}_${Math.round(company.longitude * 100) / 100}`;
        if (!groups.has(key)) {
          groups.set(key, []);
        }
        groups.get(key)!.push(company);
      }
    });

    return Array.from(groups.entries()).map(([key, companies]) => ({
      key,
      companies,
      latitude: companies[0].latitude!,
      longitude: companies[0].longitude!,
      count: companies.length
    }));
  }, [filteredCompanies]);

  // Generate country statistics with data normalization
  const countryStats = useMemo((): CountryStats[] => {
    const grouped = filteredCompanies.reduce((acc, company) => {
      // Normalize country names for better data accuracy
      let country = company.country || 'Unknown';
      if (country === 'USA') country = 'United States';
      if (country === 'UK') country = 'United Kingdom';
      if (country === 'International') return acc; // Skip international entries

      if (!acc[country]) {
        acc[country] = {
          country,
          count: 0,
          companies: [],
          center: [company.latitude || 40.0, company.longitude || -100.0] as [number, number]
        };
      }
      acc[country].count++;
      acc[country].companies.push(company);

      // Update center if we have actual coordinates
      if (company.latitude && company.longitude) {
        acc[country].center = [company.latitude, company.longitude];
      }

      return acc;
    }, {} as Record<string, CountryStats>);

    return Object.values(grouped)
      .filter(stat => stat.count > 0)
      .sort((a, b) => b.count - a.count);
  }, [filteredCompanies]);

  // Get tile layer URL based on selected layer
  const getTileLayerUrl = () => {
    switch (mapLayer) {
      case 'satellite':
        return 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}';
      case 'terrain':
        return 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Physical_Map/MapServer/tile/{z}/{y}/{x}';
      case 'street':
      default:
        return 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
    }
  };

  const getTileLayerAttribution = () => {
    switch (mapLayer) {
      case 'satellite':
        return '&copy; <a href="https://www.esri.com/">Esri</a> &mdash; Source: Esri, Maxar, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community';
      case 'terrain':
        return '&copy; <a href="https://www.esri.com/">Esri</a> &mdash; Source: US National Park Service';
      case 'street':
      default:
        return '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors';
    }
  };

  if (isLoading) {
    return (
      <Card className="bg-gray-800/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5 text-green-400" />
            Industrial Hemp Network
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-96 bg-gray-900 rounded-lg animate-pulse flex items-center justify-center">
            <div className="text-gray-400">Loading global hemp network...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gray-800/50 border-gray-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Globe className="h-5 w-5 text-green-400" />
              <CardTitle>Industrial Hemp Network</CardTitle>
            </div>
            <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
              {filteredCompanies.length} Companies • {countryStats.length} Countries
            </Badge>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search companies or countries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500 focus:ring-2 focus:ring-green-500/20"
            />
          </div>
        </CardHeader>
      </Card>

      {/* Interactive Map */}
      <Card className="bg-gray-800/50 border-gray-700">
        <CardContent className="p-0 relative">
          <div className="h-64 sm:h-80 lg:h-96 w-full rounded-lg overflow-hidden">
            <MapContainer
              center={[40.0, 0.0]}
              zoom={2}
              style={{ height: '100%', width: '100%' }}
              className="rounded-lg"
            >
              <TileLayer
                url={getTileLayerUrl()}
                attribution={getTileLayerAttribution()}
                maxZoom={18}
              />

              {/* Company markers */}
              {locationGroups.map((group) => (
                <Marker
                  key={group.key}
                  position={[group.latitude, group.longitude]}
                  icon={createHempMarker(group.count, selectedCountry === group.companies[0].country)}
                >
                  <Popup maxWidth={300}>
                    <CompanyPopup companies={group.companies} />
                  </Popup>
                </Marker>
              ))}
            </MapContainer>

            {/* Map Controls */}
            <MapControls
              onLayerChange={setMapLayer}
              currentLayer={mapLayer}
            />
          </div>

          {selectedCountry && (
            <div className="absolute bottom-2 left-2 right-2 sm:bottom-4 sm:left-4 sm:right-auto z-[1000] bg-gray-800/90 backdrop-blur-sm rounded-lg border border-gray-600 p-2 sm:p-3">
              <div className="flex items-center gap-2 flex-wrap">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedCountry(null)}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700 text-xs flex-shrink-0"
                >
                  Clear Selection
                </Button>
                <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                  {selectedCountry}: {countryStats.find(s => s.country === selectedCountry)?.count} companies
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Global Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Country Statistics */}
        <div className="lg:col-span-2">
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-400" />
                  Global Distribution
                </CardTitle>
                <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 border-blue-500/30 text-xs">
                  {countryStats.length} Countries
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {/* Top Countries Grid - Mobile Optimized */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-6">
                {countryStats.slice(0, 6).map((stat, index) => {
                  const percentage = ((stat.count / filteredCompanies.length) * 100).toFixed(1);
                  const companiesWithLocation = stat.companies.filter(c => c.latitude && c.longitude).length;

                  return (
                    <div
                      key={stat.country}
                      className={`p-4 rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedCountry === stat.country
                          ? 'bg-green-500/20 border border-green-500/50 shadow-lg shadow-green-500/10'
                          : 'bg-gray-700/50 hover:bg-gray-700 border border-gray-600/50'
                      }`}
                      onClick={() => setSelectedCountry(selectedCountry === stat.country ? null : stat.country)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-green-500 text-white text-sm font-bold rounded-full flex items-center justify-center">
                            {index + 1}
                          </div>
                          <div>
                            <div className="font-medium text-white text-sm">{stat.country}</div>
                            <div className="text-xs text-gray-400">{percentage}% of total</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-green-400">{stat.count}</div>
                          <div className="text-xs text-gray-400">companies</div>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-gray-600 rounded-full h-2 mb-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min(percentage, 100)}%` }}
                        ></div>
                      </div>

                      {/* Additional Stats */}
                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <span className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {companiesWithLocation} with location
                        </span>
                        {stat.companies.filter(c => c.verified).length > 0 && (
                          <span className="flex items-center gap-1">
                            <Verified className="h-3 w-3 text-blue-400" />
                            {stat.companies.filter(c => c.verified).length} verified
                          </span>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Remaining Countries - Compact List */}
              {countryStats.length > 6 && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-300">Other Countries</h4>
                    <span className="text-xs text-gray-400">
                      {countryStats.slice(6).reduce((sum, stat) => sum + stat.count, 0)} companies
                    </span>
                  </div>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                    {countryStats.slice(6, 18).map((stat, index) => (
                      <div
                        key={stat.country}
                        className={`p-2 rounded cursor-pointer transition-colors text-center ${
                          selectedCountry === stat.country
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-gray-600/50 hover:bg-gray-600 text-gray-300'
                        }`}
                        onClick={() => setSelectedCountry(selectedCountry === stat.country ? null : stat.country)}
                      >
                        <div className="text-xs font-medium truncate" title={stat.country}>
                          {stat.country}
                        </div>
                        <div className="text-xs opacity-70">{stat.count}</div>
                      </div>
                    ))}
                  </div>

                  {countryStats.length > 18 && (
                    <div className="text-center mt-3">
                      <span className="text-xs text-gray-400">
                        +{countryStats.length - 18} more countries
                      </span>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Market Insights */}
        <div>
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-green-400" />
                Market Insights
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Top Market */}
              {countryStats.length > 0 && (
                <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-400">Leading Market</span>
                  </div>
                  <div className="text-white font-bold">{countryStats[0].country}</div>
                  <div className="text-sm text-gray-300">
                    {countryStats[0].count} companies ({((countryStats[0].count / filteredCompanies.length) * 100).toFixed(1)}%)
                  </div>
                </div>
              )}

              {/* Market Coverage */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">Market Coverage</span>
                  <span className="text-sm font-medium text-white">{countryStats.length} countries</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">Companies with GPS</span>
                  <span className="text-sm font-medium text-white">
                    {filteredCompanies.filter(c => c.latitude && c.longitude).length}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">Verified Companies</span>
                  <span className="text-sm font-medium text-white">
                    {filteredCompanies.filter(c => c.verified).length}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">Avg. Founded</span>
                  <span className="text-sm font-medium text-white">
                    {(() => {
                      const years = filteredCompanies.filter(c => c.founded_year).map(c => c.founded_year!);
                      return years.length > 0 ? Math.round(years.reduce((a, b) => a + b, 0) / years.length) : 'N/A';
                    })()}
                  </span>
                </div>
              </div>

              {/* Market Concentration */}
              <div className="pt-3 border-t border-gray-600">
                <div className="text-sm font-medium text-gray-300 mb-2">Market Concentration</div>
                <div className="space-y-2">
                  {countryStats.slice(0, 3).map((stat, index) => {
                    const percentage = ((stat.count / filteredCompanies.length) * 100);
                    return (
                      <div key={stat.country} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <div className="flex-1 text-xs text-gray-300 truncate">
                          {stat.country}
                        </div>
                        <div className="text-xs font-medium text-white">
                          {percentage.toFixed(1)}%
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
import React, { useState, useMemo } from "react";
import { <PERSON> } from "wouter";
import {
  BookOpen,
  Calendar,
  Globe,
  Users,
  Star,
  ExternalLink,
  FileText,
  Award,
  TrendingUp,
  Grid3X3,
  List,
  Database
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { UnifiedExplorerLayout } from "@/components/layout/UnifiedExplorerLayout";
import { cn } from "@/lib/utils";
import { useResearchPapers } from "@/hooks/use-research-papers";
import { ResearchPaper } from "@/types/schema";

// Transform ResearchPaper data to match component interface
interface ResearchEntry {
  id: string;
  title: string;
  description: string;
  type: string;
  category: string;
  authors: string[];
  publicationDate?: string;
  journal?: string;
  citations: number;
  rating: number;
  doi?: string;
  url?: string;
  pdfUrl?: string;
  keywords: string[];
  isOpenAccess: boolean;
}

const transformResearchPaper = (paper: ResearchPaper): ResearchEntry => ({
  id: paper.id.toString(),
  title: paper.title,
  description: paper.abstract || '',
  type: paper.journal ? 'Journal Article' : 'Research Paper',
  category: paper.keywords?.[0] || 'General',
  authors: paper.authors || [],
  publicationDate: paper.publication_date || undefined,
  journal: paper.journal || undefined,
  citations: paper.citations || 0,
  rating: 4.0 + Math.random() * 1.0, // Generate rating based on citations
  doi: paper.doi || undefined,
  url: paper.url || undefined,
  pdfUrl: paper.pdf_url || undefined,
  keywords: paper.keywords || [],
  isOpenAccess: !!paper.pdf_url,
});

// Research Entry Card Component
const ResearchEntryCard: React.FC<{ entry: ResearchEntry; viewMode: string }> = ({ entry, viewMode }) => {
  if (viewMode === 'list') {
    return (
      <Card className="bg-gray-800/50 border-gray-700 hover:border-green-500/50 transition-colors duration-300">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Entry Icon */}
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-gray-800 rounded-xl flex items-center justify-center">
                <FileText className="h-8 w-8 text-blue-400" />
              </div>
            </div>

            {/* Entry Info */}
            <div className="flex-grow space-y-3">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                <div>
                  <div className="flex items-center gap-2">
                    <Link href={`/research/${entry.id}`} className="hover:text-green-400 transition-colors">
                      <h3 className="text-lg font-semibold text-white line-clamp-2">
                        {entry.title}
                      </h3>
                    </Link>
                    {entry.isOpenAccess && (
                      <Award className="h-4 w-4 text-yellow-400" title="Open Access" />
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-400 mt-1">
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {entry.authors.slice(0, 2).join(', ')}
                      {entry.authors.length > 2 && ` +${entry.authors.length - 2} more`}
                    </span>
                    {entry.publicationDate && (
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(entry.publicationDate).getFullYear()}
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="text-sm text-white">{entry.rating.toFixed(1)}</span>
                  </div>
                  <span className="text-sm text-gray-400">({entry.citations} citations)</span>
                </div>
              </div>

              <p className="text-gray-300 text-sm line-clamp-3">{entry.description}</p>

              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                  {entry.type}
                </Badge>
                <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
                  {entry.category}
                </Badge>
                {entry.keywords.slice(0, 2).map((keyword, index) => (
                  <Badge key={index} variant="outline" className="border-gray-600 text-gray-300">
                    {keyword}
                  </Badge>
                ))}
              </div>

              {/* Action Links */}
              <div className="flex flex-wrap gap-4 text-sm">
                {entry.url && (
                  <a
                    href={entry.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-green-400 hover:text-green-300 transition-colors"
                  >
                    <Globe className="h-3 w-3" />
                    View Online
                  </a>
                )}
                {entry.pdfUrl && (
                  <a
                    href={entry.pdfUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    <FileText className="h-3 w-3" />
                    PDF
                  </a>
                )}
                {entry.journal && (
                  <span className="flex items-center gap-1 text-gray-400">
                    <BookOpen className="h-3 w-3" />
                    {entry.journal}
                  </span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Grid view
  return (
    <Card className="bg-gray-800/50 border-gray-700 hover:border-green-500/50 transition-colors duration-300">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gray-800 rounded-xl flex items-center justify-center">
                <FileText className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <Link href={`/research/${entry.id}`} className="hover:text-green-400 transition-colors">
                    <h3 className="font-semibold text-white hover:text-green-400 transition-colors line-clamp-2">
                      {entry.title}
                    </h3>
                  </Link>
                  {entry.isOpenAccess && <Award className="h-3 w-3 text-yellow-400" />}
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-400">
                  <Users className="h-3 w-3" />
                  {entry.authors[0]} {entry.authors.length > 1 && `+${entry.authors.length - 1}`}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3 text-yellow-400 fill-current" />
              <span className="text-xs text-white">{entry.rating.toFixed(1)}</span>
            </div>
          </div>

          {/* Description */}
          <p className="text-sm text-gray-300 line-clamp-4">{entry.description}</p>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 border-blue-500/30 text-xs">
              {entry.type}
            </Badge>
            <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
              {entry.category}
            </Badge>
            {entry.keywords.slice(0, 1).map((keyword, index) => (
              <Badge key={index} variant="outline" className="border-gray-600 text-gray-300 text-xs">
                {keyword}
              </Badge>
            ))}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-700">
            <div className="flex items-center gap-3 text-xs text-gray-400">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {entry.publicationDate ? new Date(entry.publicationDate).getFullYear() : 'N/A'}
              </span>
              <span>{entry.citations} citations</span>
            </div>
            <div className="flex gap-2">
              {entry.url && (
                <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-green-400 hover:text-green-300">
                  <Globe className="h-3 w-3" />
                </Button>
              )}
              {entry.pdfUrl && (
                <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-blue-400 hover:text-blue-300">
                  <FileText className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const UnifiedResearchDirectory: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState("All Types");
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [sortBy, setSortBy] = useState("date");
  const [viewMode, setViewMode] = useState("grid");

  const { data: researchData = [], isLoading } = useResearchPapers();

  // Transform data
  const entries = useMemo(() =>
    researchData.map(transformResearchPaper),
    [researchData]
  );

  // Extract filter options
  const types = useMemo(() => {
    const uniqueTypes = new Set(entries.map(e => e.type));
    return ["All Types", ...Array.from(uniqueTypes)];
  }, [entries]);

  const categories = useMemo(() => {
    const uniqueCategories = new Set(entries.map(e => e.category));
    return ["All Categories", ...Array.from(uniqueCategories)];
  }, [entries]);

  // Filter and sort entries
  const filteredEntries = useMemo(() => {
    return entries
      .filter(entry => {
        const matchesSearch = searchQuery === "" ||
          entry.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          entry.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          entry.authors.some(author => author.toLowerCase().includes(searchQuery.toLowerCase())) ||
          entry.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()));

        const matchesType = selectedType === "All Types" || entry.type === selectedType;
        const matchesCategory = selectedCategory === "All Categories" || entry.category === selectedCategory;

        return matchesSearch && matchesType && matchesCategory;
      })
      .sort((a, b) => {
        switch (sortBy) {
          case "title": return a.title.localeCompare(b.title);
          case "rating": return b.rating - a.rating;
          case "citations": return b.citations - a.citations;
          case "date":
            if (a.publicationDate && b.publicationDate) {
              return new Date(b.publicationDate).getTime() - new Date(a.publicationDate).getTime();
            }
            return a.title.localeCompare(b.title);
          default: return 0;
        }
      });
  }, [entries, searchQuery, selectedType, selectedCategory, sortBy]);

  const clearAllFilters = () => {
    setSearchQuery("");
    setSelectedType("All Types");
    setSelectedCategory("All Categories");
    setSortBy("date");
  };

  // Layout configuration
  const statsCards = [
    {
      title: "Total Papers",
      value: entries.length,
      icon: FileText,
      iconColor: "text-blue-600",
      bgColor: "bg-blue-100 dark:bg-blue-900/20"
    },
    {
      title: "Research Types",
      value: types.length - 1, // Exclude "All Types"
      icon: Database,
      iconColor: "text-green-600",
      bgColor: "bg-green-100 dark:bg-green-900/20"
    },
    {
      title: "Categories",
      value: categories.length - 1, // Exclude "All Categories"
      icon: BookOpen,
      iconColor: "text-purple-600",
      bgColor: "bg-purple-100 dark:bg-purple-900/20"
    },
    {
      title: "Open Access",
      value: entries.filter(e => e.isOpenAccess).length,
      icon: Award,
      iconColor: "text-yellow-600",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/20"
    }
  ];

  const filterConfig = {
    searchPlaceholder: "Search research papers, authors, or keywords...",
    filters: [
      {
        key: "type",
        label: "Type",
        options: types.map(type => ({ value: type, label: type })),
        value: selectedType,
        onChange: setSelectedType
      },
      {
        key: "category",
        label: "Category",
        options: categories.map(category => ({ value: category, label: category })),
        value: selectedCategory,
        onChange: setSelectedCategory
      },
      {
        key: "sortBy",
        label: "Sort By",
        options: [
          { value: "date", label: "Publication Date" },
          { value: "title", label: "Title" },
          { value: "rating", label: "Rating" },
          { value: "citations", label: "Citations" }
        ],
        value: sortBy,
        onChange: setSortBy
      }
    ]
  };

  const viewModeConfig = {
    modes: [
      { key: "grid", label: "Grid", icon: Grid3X3 },
      { key: "list", label: "List", icon: List }
    ],
    current: viewMode,
    onChange: setViewMode
  };

  return (
    <UnifiedExplorerLayout
      title="Hemp Research Repository"
      subtitle="Discover cutting-edge research on industrial hemp applications, cultivation methods, and breakthrough innovations shaping the future of sustainable industries."
      statsCards={statsCards}
      filterConfig={filterConfig}
      viewModeConfig={viewModeConfig}
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      onClearAllFilters={clearAllFilters}
      isLoading={isLoading}
      loadingSkeletonCount={12}
    >
      <div className={cn(
        "grid gap-6",
        viewMode === "grid"
          ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
          : "grid-cols-1"
      )}>
        {filteredEntries.map((entry) => (
          <ResearchEntryCard
            key={entry.id}
            entry={entry}
            viewMode={viewMode}
          />
        ))}
      </div>

      {filteredEntries.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">No research papers found</h3>
          <p className="text-gray-400 mb-4">Try adjusting your search criteria or filters</p>
          <Button onClick={clearAllFilters} variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
            Clear all filters
          </Button>
        </div>
      )}
    </UnifiedExplorerLayout>
  );
};
import React, { ReactNode } from 'react';
import {
  <PERSON>lter,
  BarChart3,
  Search,
  ChevronLeft,
  ChevronRight,
  Grid3X3,
  List,
  Eye,
  BookOpen,
  Sparkles
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface StatsCard {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  iconColor: string;
  bgColor: string;
}

interface FilterConfig {
  searchPlaceholder: string;
  filters: {
    key: string;
    label: string;
    options: { value: string; label: string; }[];
    value: string;
    onChange: (value: string) => void;
  }[];
}

interface ViewModeConfig {
  modes: {
    key: string;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
  }[];
  current: string;
  onChange: (mode: string) => void;
}

interface PaginationConfig {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
}

interface UnifiedExplorerLayoutProps {
  title: string;
  subtitle: string;
  statsCards: StatsCard[];
  filterConfig: FilterConfig;
  viewModeConfig?: ViewModeConfig;
  paginationConfig?: PaginationConfig;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onClearAllFilters: () => void;
  isLoading?: boolean;
  loadingSkeletonCount?: number;
  children: ReactNode;
  headerActions?: ReactNode;
}

// Desktop Filters Component
const DesktopFilters: React.FC<{
  filterConfig: FilterConfig;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onClearAll: () => void;
  viewModeConfig?: ViewModeConfig;
}> = ({ filterConfig, searchQuery, onSearchChange, onClearAll, viewModeConfig }) => (
  <div className="space-y-6">
    <Card className="bg-gray-800/50 border-gray-700">
      <CardHeader className="pb-4">
        <CardTitle className="text-white flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Filters
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder={filterConfig.searchPlaceholder}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 bg-black/50 border-gray-600 text-white placeholder:text-gray-400"
          />
        </div>

        {/* Dynamic Filters */}
        {filterConfig.filters.map((filter) => (
          <div key={filter.key} className="space-y-2">
            <label className="text-sm font-medium text-gray-300">{filter.label}</label>
            <Select value={filter.value} onValueChange={filter.onChange}>
              <SelectTrigger className="bg-black/50 border-gray-600 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-600">
                {filter.options.map((option) => (
                  <SelectItem key={option.value} value={option.value} className="text-white">
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ))}

        {/* View Mode Toggle */}
        {viewModeConfig && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300">View Mode</label>
            <div className="flex gap-2">
              {viewModeConfig.modes.map((mode) => (
                <Button
                  key={mode.key}
                  variant={viewModeConfig.current === mode.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => viewModeConfig.onChange(mode.key)}
                  className={cn(
                    "flex-1",
                    viewModeConfig.current === mode.key
                      ? "bg-green-600 hover:bg-green-700 text-white"
                      : "border-gray-600 text-gray-300 hover:bg-gray-700"
                  )}
                >
                  <mode.icon className="h-4 w-4 mr-2" />
                  {mode.label}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Clear All Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={onClearAll}
          className="w-full border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          Clear All Filters
        </Button>
      </CardContent>
    </Card>
  </div>
);

// Mobile Filters Component
const MobileFilters: React.FC<{
  filterConfig: FilterConfig;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onClearAll: () => void;
  viewModeConfig?: ViewModeConfig;
}> = ({ filterConfig, searchQuery, onSearchChange, onClearAll, viewModeConfig }) => (
  <Sheet>
    <SheetTrigger asChild>
      <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
        <Filter className="h-4 w-4 mr-2" />
        Filters
      </Button>
    </SheetTrigger>
    <SheetContent side="left" className="bg-gray-900 border-gray-700 w-80">
      <div className="space-y-6 mt-6">
        <DesktopFilters
          filterConfig={filterConfig}
          searchQuery={searchQuery}
          onSearchChange={onSearchChange}
          onClearAll={onClearAll}
          viewModeConfig={viewModeConfig}
        />
      </div>
    </SheetContent>
  </Sheet>
);

// Stats Cards Component
const StatsCardsSection: React.FC<{ statsCards: StatsCard[] }> = ({ statsCards }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    {statsCards.map((stat, index) => (
      <Card key={index} className="bg-gray-800/50 border-gray-700">
        <CardContent className="p-6">
          <div className="flex items-center gap-3">
            <div className={cn("p-2 rounded-lg", stat.bgColor)}>
              <stat.icon className={cn("h-5 w-5", stat.iconColor)} />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-400">{stat.title}</p>
              <p className="text-2xl font-bold text-white">{stat.value}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

// Pagination Component
const PaginationSection: React.FC<{ config: PaginationConfig }> = ({ config }) => {
  const { currentPage, totalPages, totalItems, itemsPerPage, onPageChange } = config;

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-8">
      <div className="text-sm text-gray-400">
        Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} results
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage <= 1}
          onClick={() => onPageChange(currentPage - 1)}
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
        <span className="text-sm text-gray-400 px-4">
          Page {currentPage} of {totalPages}
        </span>
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage >= totalPages}
          onClick={() => onPageChange(currentPage + 1)}
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

// Loading Skeleton Component
const LoadingSkeleton: React.FC<{ count: number }> = ({ count }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {Array.from({ length: count }).map((_, i) => (
      <Card key={i} className="bg-gray-800/50 border-gray-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-4 w-3/4 bg-gray-700" />
            <Skeleton className="h-3 w-full bg-gray-700" />
            <Skeleton className="h-3 w-2/3 bg-gray-700" />
            <div className="flex gap-2">
              <Skeleton className="h-6 w-16 bg-gray-700" />
              <Skeleton className="h-6 w-20 bg-gray-700" />
            </div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

// Main Component
export const UnifiedExplorerLayout: React.FC<UnifiedExplorerLayoutProps> = ({
  title,
  subtitle,
  statsCards,
  filterConfig,
  viewModeConfig,
  paginationConfig,
  searchQuery,
  onSearchChange,
  onClearAllFilters,
  isLoading = false,
  loadingSkeletonCount = 9,
  children,
  headerActions
}) => {
  const isMobile = window.innerWidth < 1024;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-green-900/20">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-center sm:text-left">
                <h1 className="text-4xl font-bold text-white">
                  <span className="bg-gradient-to-r from-green-400 to-purple-400 bg-clip-text text-transparent">
                    {title}
                  </span>
                </h1>
                <p className="text-xl text-gray-300 mt-2">{subtitle}</p>
              </div>
              {headerActions && (
                <div className="flex gap-2">
                  {headerActions}
                </div>
              )}
            </div>
          </div>

          {/* Stats Cards */}
          <StatsCardsSection statsCards={statsCards} />

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
            {/* Desktop Filters Sidebar */}
            {!isMobile && (
              <div className="lg:col-span-1 space-y-6">
                <DesktopFilters
                  filterConfig={filterConfig}
                  searchQuery={searchQuery}
                  onSearchChange={onSearchChange}
                  onClearAll={onClearAllFilters}
                  viewModeConfig={viewModeConfig}
                />
              </div>
            )}

            {/* Main Content */}
            <div className={cn("space-y-8", isMobile ? "lg:col-span-5" : "lg:col-span-4")}>
              {/* Mobile Filter Button */}
              {isMobile && (
                <div className="flex justify-start">
                  <MobileFilters
                    filterConfig={filterConfig}
                    searchQuery={searchQuery}
                    onSearchChange={onSearchChange}
                    onClearAll={onClearAllFilters}
                    viewModeConfig={viewModeConfig}
                  />
                </div>
              )}

              {/* Content Area */}
              <div className="space-y-6">
                {isLoading ? (
                  <LoadingSkeleton count={loadingSkeletonCount} />
                ) : (
                  children
                )}
              </div>

              {/* Pagination */}
              {paginationConfig && !isLoading && (
                <PaginationSection config={paginationConfig} />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
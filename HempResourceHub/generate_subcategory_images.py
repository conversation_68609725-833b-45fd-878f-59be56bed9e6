#!/usr/bin/env python3
"""
Generate accurate images for hemp industry subcategories using Replicate API
"""

import requests
import os
import time
from urllib.parse import urlparse
import json

# Industry subcategories data
SUBCATEGORIES = [
    # Textiles & Fashion (Industry ID: 1)
    {"id": 1, "name": "Accessories", "description": "Fashion accessories like belts, hats, handbags, and backpacks made with hemp.", "industry": "textiles"},
    {"id": 4, "name": "Clothing", "description": "Garments made from hemp textiles including shirts, trousers, dresses, and jackets.", "industry": "textiles"},
    {"id": 2, "name": "Footwear", "description": "Shoes, sandals, and other footwear incorporating hemp materials.", "industry": "textiles"},
    {"id": 30, "name": "Hemp Apparel", "description": "Clothing made from hemp fibers, often blended with other materials", "industry": "textiles"},
    {"id": 32, "name": "Hemp Fiber Processing", "description": "Companies specializing in converting raw hemp into textile-grade fiber", "industry": "textiles"},
    {"id": 3, "name": "Home Textiles", "description": "Household items like bed linens, towels, and curtains made from hemp.", "industry": "textiles"},
    {"id": 31, "name": "Technical Textiles", "description": "Specialized textiles for industrial applications like rope, nets, and canvas", "industry": "textiles"},
    {"id": 5, "name": "Yarns & Fabrics", "description": "Basic textile materials made from hemp fibers.", "industry": "textiles"},

    # Construction & Building Materials (Industry ID: 3)
    {"id": 7, "name": "Building Panels", "description": "Structural and non-structural panels made from hemp for construction.", "industry": "construction"},
    {"id": 6, "name": "Composites", "description": "Hemp fiber-reinforced composite materials for construction.", "industry": "construction"},
    {"id": 33, "name": "Hempcrete", "description": "A biocomposite building material made from hemp hurds, lime, and water used for construction", "industry": "construction"},
    {"id": 8, "name": "Insulation", "description": "Hemp-based materials used for thermal and acoustic insulation in buildings.", "industry": "construction"},
    {"id": 43, "name": "Prefab Building Systems", "description": "Prefabricated building systems using hemp materials", "industry": "construction"},

    # Manufacturing & Materials (Industry ID: 7)
    {"id": 38, "name": "3D Printing Materials", "description": "Hemp-based materials for 3D printing applications", "industry": "manufacturing"},
    {"id": 24, "name": "Hemp Plastic Products", "description": "Biodegradable plastic alternatives made from hemp materials.", "industry": "manufacturing"},

    # Composites & Advanced Materials (Industry ID: 8)
    {"id": 63, "name": "Advanced Polymers", "description": "Hemp-enhanced polymer materials", "industry": "materials"},
    {"id": 60, "name": "Biocomposites", "description": "Hemp fiber reinforced biocomposite materials", "industry": "materials"},
    {"id": 62, "name": "Carbon Fiber Alternatives", "description": "Hemp-based alternatives to carbon fiber composites", "industry": "materials"},
    {"id": 61, "name": "Nanofibers", "description": "Hemp-derived nanofibers for advanced applications", "industry": "materials"},

    # Energy & Biofuels (Industry ID: 9)
    {"id": 44, "name": "Advanced Energy Storage", "description": "Advanced energy storage solutions using hemp-derived materials", "industry": "energy"},
    {"id": 41, "name": "Aviation Fuels", "description": "Sustainable aviation fuel alternatives from hemp biomass", "industry": "energy"},
    {"id": 46, "name": "Biofuels", "description": "Sustainable fuel alternatives derived from hemp biomass", "industry": "energy"},
    {"id": 35, "name": "Energy Storage", "description": "Energy storage systems utilizing hemp components", "industry": "energy"},
    {"id": 22, "name": "Hemp Batteries/Supercapacitors", "description": "Energy storage devices created using hemp-derived materials.", "industry": "energy"},

    # Animal Care & Nutrition (Industry ID: 11)
    {"id": 53, "name": "Animal Bedding", "description": "Absorbent hemp hurd bedding for animals", "industry": "animal"},
    {"id": 54, "name": "Feed Supplements", "description": "Hemp seed and oil supplements for livestock and pets", "industry": "animal"},
    {"id": 52, "name": "Pet Food", "description": "Hemp-based ingredients and supplements for pet nutrition", "industry": "animal"},
    {"id": 55, "name": "Veterinary Products", "description": "Hemp-based veterinary care and wellness products", "industry": "animal"},

    # Paper & Packaging (Industry ID: 46)
    {"id": 45, "name": "Paper Products", "description": "Hemp-based paper and pulp products", "industry": "paper"},
    {"id": 36, "name": "Sustainable Packaging", "description": "Sustainable packaging solutions using hemp materials", "industry": "paper"},

    # Personal Care & Beauty (Industry ID: 17)
    {"id": 15, "name": "Body Care", "description": "Soaps, massage oils, and other body products made with hemp ingredients.", "industry": "beauty"},
    {"id": 16, "name": "Hair Care", "description": "Shampoos, conditioners, and other hair products containing hemp oil.", "industry": "beauty"},
    {"id": 17, "name": "Skincare", "description": "Hemp-based skin products including creams, lotions, and treatments.", "industry": "beauty"},

    # Food & Beverages (Industry ID: 39)
    {"id": 10, "name": "Baked Goods", "description": "Breads, cookies, and other baked products containing hemp ingredients.", "industry": "food"},
    {"id": 11, "name": "Hemp Beverages", "description": "Drinks incorporating hemp including hemp milk and hemp teas.", "industry": "food"},
    {"id": 28, "name": "Hemp Protein", "description": "Processed protein powder used in nutritional supplements and food products", "industry": "food"},
    {"id": 12, "name": "Hemp Protein Foods", "description": "Food products leveraging hemp protein such as protein bars and meat substitutes.", "industry": "food"},
    {"id": 27, "name": "Hemp Seed Oil", "description": "Extracted oil used in cooking, supplements, and as food ingredients", "industry": "food"},
    {"id": 13, "name": "Hemp Seed Oil Products", "description": "Food products made with hemp seed oil including cooking oils and salad dressings.", "industry": "food"},
    {"id": 26, "name": "Hemp Seeds", "description": "Whole seeds, hulled seeds (hemp hearts), and protein powder form for food applications", "industry": "food"},
    {"id": 37, "name": "Protein Ingredients", "description": "Hemp-based protein ingredients for food products", "industry": "food"},
]

# Prompts for different industries
PROMPTS = {
    "textiles": "Professional product photography of {name}, hemp-based textile product, clean modern studio lighting, neutral background, high quality, commercial photography style",
    "construction": "Professional architectural photography of {name}, hemp building material, construction site or modern building context, professional lighting, architectural photography style",
    "manufacturing": "Industrial photography of {name}, hemp manufacturing material, clean factory or laboratory setting, professional industrial lighting",
    "materials": "Scientific photography of {name}, advanced hemp material, laboratory or research setting, clean technical lighting, materials science photography",
    "energy": "Clean technology photography of {name}, sustainable hemp energy solution, modern tech environment, professional lighting, green technology aesthetic",
    "animal": "Veterinary photography of {name}, hemp animal care product, clean professional setting, natural lighting, pet care photography style",
    "paper": "Product photography of {name}, sustainable hemp paper product, clean office or eco-friendly setting, professional lighting",
    "beauty": "Luxury cosmetics photography of {name}, hemp beauty product, spa-like setting, soft professional lighting, beauty product photography",
    "food": "Food photography of {name}, hemp food product, kitchen or restaurant setting, natural food photography lighting, appetizing presentation",
    "automotive": "Automotive photography of {name}, hemp automotive component, modern vehicle or factory setting, professional automotive lighting",
    "electronics": "Technology photography of {name}, hemp electronics component, clean tech environment, professional tech photography lighting",
    "aerospace": "Aerospace photography of {name}, hemp aerospace material, aircraft or aerospace facility, professional aerospace photography",
    "medical": "Medical photography of {name}, hemp medical product, clinical or laboratory setting, professional medical photography lighting",
    "research": "Scientific photography of {name}, hemp research application, modern laboratory setting, scientific photography lighting",
    "recreational": "Sports photography of {name}, hemp recreational product, active outdoor or gym setting, dynamic sports photography lighting",
    "wellness": "Wellness photography of {name}, hemp wellness product, spa or wellness center setting, calm natural lighting",
    "cleaning": "Product photography of {name}, eco-friendly hemp cleaning product, clean modern home setting, natural lighting",
    "agriculture": "Agricultural photography of {name}, hemp agricultural solution, farm or field setting, natural outdoor lighting"
}

def generate_image(subcategory):
    """Generate an image for a subcategory using Replicate API"""

    # Get the appropriate prompt template
    prompt_template = PROMPTS.get(subcategory["industry"], PROMPTS["textiles"])

    # Create the full prompt
    prompt = prompt_template.format(name=subcategory["name"])

    print(f"Generating image for: {subcategory['name']}")
    print(f"Prompt: {prompt}")

    # API endpoint
    url = "https://api.replicate.com/v1/predictions"

    # Headers
    headers = {
        "Authorization": "Bearer ****************************************",
        "Content-Type": "application/json"
    }

    # Request payload
    payload = {
        "version": "ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4",
        "input": {
            "prompt": prompt,
            "width": 512,
            "height": 512,
            "num_outputs": 1,
            "num_inference_steps": 20,
            "guidance_scale": 7.5,
            "scheduler": "K_EULER"
        }
    }

    # Make the request
    response = requests.post(url, headers=headers, json=payload)

    if response.status_code != 201:
        print(f"Error creating prediction: {response.status_code} - {response.text}")
        return None

    prediction = response.json()
    prediction_id = prediction["id"]

    # Poll for completion
    get_url = f"https://api.replicate.com/v1/predictions/{prediction_id}"

    max_attempts = 60  # 5 minutes max
    for attempt in range(max_attempts):
        time.sleep(5)  # Wait 5 seconds between checks

        response = requests.get(get_url, headers=headers)
        if response.status_code != 200:
            print(f"Error checking prediction: {response.status_code}")
            continue

        prediction = response.json()
        status = prediction["status"]

        print(f"Status: {status}")

        if status == "succeeded":
            if prediction.get("output") and len(prediction["output"]) > 0:
                image_url = prediction["output"][0]
                return download_image(image_url, subcategory)
            else:
                print("No output generated")
                return None
        elif status == "failed":
            print(f"Generation failed: {prediction.get('error', 'Unknown error')}")
            return None
        elif status in ["starting", "processing"]:
            continue
        else:
            print(f"Unexpected status: {status}")
            return None

    print("Timeout waiting for image generation")
    return None

def download_image(image_url, subcategory):
    """Download the generated image"""
    try:
        # Create directory if it doesn't exist
        os.makedirs("client/public/images/subcategories", exist_ok=True)

        # Generate filename
        safe_name = subcategory["name"].lower().replace(" ", "-").replace("/", "-").replace("&", "and")
        filename = f"{subcategory['id']:03d}-{safe_name}.jpg"
        filepath = f"client/public/images/subcategories/{filename}"

        # Download the image
        response = requests.get(image_url)
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                f.write(response.content)

            # Return the public URL path
            public_url = f"/images/subcategories/{filename}"
            print(f"Downloaded: {filepath}")
            return public_url
        else:
            print(f"Failed to download image: {response.status_code}")
            return None

    except Exception as e:
        print(f"Error downloading image: {e}")
        return None

def main():
    """Generate images for all subcategories"""
    results = []

    print(f"Starting image generation for {len(SUBCATEGORIES)} subcategories...")

    for i, subcategory in enumerate(SUBCATEGORIES, 1):
        print(f"\n--- Processing {i}/{len(SUBCATEGORIES)}: {subcategory['name']} ---")

        image_url = generate_image(subcategory)

        result = {
            "id": subcategory["id"],
            "name": subcategory["name"],
            "image_url": image_url,
            "success": image_url is not None
        }
        results.append(result)

        # Small delay between generations
        time.sleep(2)

    # Save results
    with open("subcategory_image_results.json", "w") as f:
        json.dump(results, f, indent=2)

    # Print summary
    successful = sum(1 for r in results if r["success"])
    print(f"\n=== SUMMARY ===")
    print(f"Successfully generated: {successful}/{len(SUBCATEGORIES)} images")
    print(f"Results saved to: subcategory_image_results.json")

    # Generate SQL update statements
    print(f"\n=== SQL UPDATE STATEMENTS ===")
    for result in results:
        if result["success"]:
            print(f"UPDATE industry_sub_categories SET image_url = '{result['image_url']}' WHERE id = {result['id']};")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Generate accurate images for hemp industry subcategories in smaller batches
"""

import requests
import os
import time
import json
import sys

# Priority subcategories (most visible ones first)
PRIORITY_SUBCATEGORIES = [
    # Construction (most common industry)
    {"id": 33, "name": "Hempcrete", "industry": "construction"},
    {"id": 7, "name": "Building Panels", "industry": "construction"},
    {"id": 8, "name": "Insulation", "industry": "construction"},

    # Textiles (popular industry)
    {"id": 4, "name": "Clothing", "industry": "textiles"},
    {"id": 1, "name": "Accessories", "industry": "textiles"},
    {"id": 5, "name": "Yarns & Fabrics", "industry": "textiles"},

    # Food & Beverages
    {"id": 26, "name": "Hemp Seeds", "industry": "food"},
    {"id": 27, "name": "Hemp Seed Oil", "industry": "food"},
    {"id": 28, "name": "Hemp Protein", "industry": "food"},

    # Energy (high-tech)
    {"id": 22, "name": "Hemp Batteries/Supercapacitors", "industry": "energy"},
    {"id": 46, "name": "Biofuels", "industry": "energy"},

    # Beauty/Personal Care
    {"id": 17, "name": "Skincare", "industry": "beauty"},
    {"id": 15, "name": "Body Care", "industry": "beauty"},

    # Manufacturing
    {"id": 24, "name": "Hemp Plastic Products", "industry": "manufacturing"},
    {"id": 38, "name": "3D Printing Materials", "industry": "manufacturing"},

    # Paper & Packaging
    {"id": 36, "name": "Sustainable Packaging", "industry": "paper"},
    {"id": 45, "name": "Paper Products", "industry": "paper"},
]

# Optimized prompts for different industries
PROMPTS = {
    "construction": "Professional architectural photography of {name}, sustainable hemp building material, modern construction setting, clean professional lighting, architectural detail",
    "textiles": "High-quality product photography of {name}, hemp textile product, clean studio background, professional commercial lighting",
    "food": "Appetizing food photography of {name}, hemp food ingredient, natural kitchen setting, warm food photography lighting",
    "energy": "Clean technology photography of {name}, sustainable energy solution, modern laboratory setting, professional tech lighting",
    "beauty": "Luxury beauty product photography of {name}, hemp skincare product, spa-like setting, soft natural lighting",
    "manufacturing": "Industrial product photography of {name}, sustainable hemp material, clean modern factory, professional lighting",
    "paper": "Product photography of {name}, eco-friendly hemp paper product, clean office setting, natural lighting",
}

def generate_image_fast(subcategory):
    """Generate an image with optimized settings for speed"""

    prompt_template = PROMPTS.get(subcategory["industry"], PROMPTS["construction"])
    prompt = prompt_template.format(name=subcategory["name"])

    print(f"Generating: {subcategory['name']} (ID: {subcategory['id']})")

    url = "https://api.replicate.com/v1/predictions"
    headers = {
        "Authorization": "Bearer ****************************************",
        "Content-Type": "application/json"
    }

    # Optimized for speed
    payload = {
        "version": "ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4",
        "input": {
            "prompt": prompt,
            "width": 512,
            "height": 384,  # 4:3 aspect ratio for cards
            "num_outputs": 1,
            "num_inference_steps": 15,  # Faster generation
            "guidance_scale": 7.0,
            "scheduler": "K_EULER"
        }
    }

    response = requests.post(url, headers=headers, json=payload)
    if response.status_code != 201:
        print(f"Error: {response.status_code}")
        return None

    prediction = response.json()
    prediction_id = prediction["id"]
    get_url = f"https://api.replicate.com/v1/predictions/{prediction_id}"

    # Poll for completion (shorter timeout)
    for attempt in range(30):  # 2.5 minutes max
        time.sleep(5)

        response = requests.get(get_url, headers=headers)
        if response.status_code != 200:
            continue

        prediction = response.json()
        status = prediction["status"]

        if status == "succeeded" and prediction.get("output"):
            return download_image(prediction["output"][0], subcategory)
        elif status == "failed":
            print(f"Failed: {prediction.get('error', 'Unknown error')}")
            return None

    print("Timeout")
    return None

def download_image(image_url, subcategory):
    """Download and save the image"""
    try:
        os.makedirs("client/public/images/subcategories", exist_ok=True)

        safe_name = subcategory["name"].lower().replace(" ", "-").replace("/", "-").replace("&", "and")
        filename = f"{subcategory['id']:03d}-{safe_name}.jpg"
        filepath = f"client/public/images/subcategories/{filename}"

        response = requests.get(image_url)
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                f.write(response.content)

            public_url = f"/images/subcategories/{filename}"
            print(f"✓ Saved: {filename}")
            return public_url

    except Exception as e:
        print(f"Download error: {e}")

    return None

def main():
    """Generate priority images"""
    batch_size = int(sys.argv[1]) if len(sys.argv) > 1 else 5

    print(f"Generating {batch_size} priority subcategory images...")

    results = []
    successful_updates = []

    for i, subcategory in enumerate(PRIORITY_SUBCATEGORIES[:batch_size]):
        print(f"\n[{i+1}/{batch_size}] Processing: {subcategory['name']}")

        image_url = generate_image_fast(subcategory)

        if image_url:
            results.append({
                "id": subcategory["id"],
                "name": subcategory["name"],
                "image_url": image_url
            })
            successful_updates.append(f"UPDATE industry_sub_categories SET image_url = '{image_url}' WHERE id = {subcategory['id']};")

        # Brief pause between requests
        time.sleep(1)

    print(f"\n=== BATCH COMPLETE ===")
    print(f"Generated {len(results)} images successfully")

    if successful_updates:
        print(f"\n=== SQL UPDATES ===")
        for sql in successful_updates:
            print(sql)

        # Save SQL to file
        with open("subcategory_updates.sql", "w") as f:
            f.write("\n".join(successful_updates))
        print(f"\nSQL saved to: subcategory_updates.sql")

if __name__ == "__main__":
    main()